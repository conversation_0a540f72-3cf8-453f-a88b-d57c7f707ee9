/* Survey responses pattern styles */
:host {
  display: block;
  width: 100%;
}

c-card {
  width: 100%;
}

.responses-table-placeholder {
  padding: 2em;
  text-align: center;
}

.responses-table-placeholder ul {
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

/* Response Table Styles */
.response-table {
  width: 100%;
  border-collapse: collapse;
}

.table-header {
  display: flex;
  border-bottom: 2px solid var(--color__grey--200);
  font-weight: bold;
  padding-bottom: 1em;
  align-items: flex-start;
}

.table-row {
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid var(--color__grey--100);
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 1em 0;
}

.table-row:hover {
  background-color: var(--color__grey--25);
}

.table-cell {
  flex: 1;
  padding: 0 1em;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.table-cell-action {
  flex: none;
  width: auto;
  padding: 0 1em;
  text-align: center;
}

.delete-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2em;
  padding: 0.25em;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  color: var(--color__red--600);
}

.delete-button:hover {
  background-color: var(--color__red--50);
}

/* Response Modal Content Styles */

.response-section {
  margin-bottom: 1.5em;
  padding-bottom: 1em;
  border-bottom: 1px solid var(--color__grey--100);
}

.response-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.response-section pre {
  background-color: var(--color__grey--50);
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.9em;
  margin-top: 0.5em;
}

.status-badge {
  padding: 0.25em 0.5em;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
}

.status-badge.completed {
  background-color: var(--color__green--100);
  color: var(--color__green--800);
}

.status-badge.discarded {
  background-color: var(--color__red--100);
  color: var(--color__red--800);
}

.response-section {
  margin-bottom: 1.5em;
  padding-bottom: 1em;
  border-bottom: 1px solid var(--color__grey--100);
}

.response-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.response-section pre {
  background-color: var(--color__grey--50);
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.9em;
  margin-top: 0.5em;
}

/* Pagination Styles */
.pagination-controls {
  border-top: 1px solid var(--color__grey--200);
  padding-top: 1em;
}

.pagination-button {
  background-color: var(--color__indigo--600);
  color: white;
  border: none;
  padding: 0.5em 1em;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s ease;
  margin: 0 0.5em;
}

.pagination-button:hover:not(:disabled) {
  background-color: var(--color__indigo--700);
}

.pagination-button:disabled {
  background-color: var(--color__grey--300);
  cursor: not-allowed;
}

.pagination-info {
  margin: 0 1em;
  font-size: 0.9em;
  color: var(--color__grey--700);
}

.pagination-number {
  background-color: white;
  color: var(--color__indigo--600);
  border: 1px solid var(--color__indigo--600);
  padding: 0.5em 0.75em;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  margin: 0 0.25em;
  transition: all 0.2s ease;
  min-width: 40px;
}

.pagination-number:hover {
  background-color: var(--color__indigo--50);
}

.pagination-number.active {
  background-color: var(--color__indigo--600);
  color: white;
}
