import { Request, Response } from 'express';

import { readResponses } from '../dals';
import { verifyResourceOwnership } from '../../security/helpers';
import { Var } from '../../../global/var';
import { logger } from '../../../global/services';

export const getResponsesController = async (req: Request, res: Response) => {
  const surveyId = res.locals.surveyId;
  const accountId = res.locals.accountId;

  // Extract query parameters for filtering
  const timeFilter = req.query.timeFilter as string;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;

  logger.info('Responses request', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    filters: { timeFilter, page, limit },
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('survey', surveyId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized responses access attempt', {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  const result = await readResponses(surveyId, accountId, {
    timeFilter,
    page,
    limit,
  });

  if (!result.success) {
    return res.status(400).json({
      success: false,
      message: result.message,
    });
  }

  return res.status(200).json({
    success: true,
    message: result.message,
    payload: result.payload,
  });
};
