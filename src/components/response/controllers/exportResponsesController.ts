import { Request, Response } from 'express';
import { exportResponsesAsCsv } from '../dals';
import { verifyResourceOwnership } from '../../security/helpers';
import { logger } from '../../../global/services';

export const exportResponsesController = async (req: Request, res: Response) => {
  const surveyId = res.locals.surveyId;
  const accountId = res.locals.accountId;
  const includeDiscarded = req.query.includeDiscarded === 'true';

  logger.info('Response export attempt', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    includeDiscarded,
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('survey', surveyId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized response export attempt', {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  const result: any = await exportResponsesAsCsv(surveyId, accountId, includeDiscarded);

  if (!result.success) {
    return res.status(400).json({
      success: false,
      message: result.message,
    });
  }

  // Set headers for file download
  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', `attachment; filename="${result.payload.filename}"`);

  return res.status(200).send(result.payload.csv);
};
